# 相机-IMU联合标定完整流程

本项目提供了无人机上相机与IMU联合标定的完整解决方案，使用imu_utils标定IMU内参，使用Kalibr工具包进行相机内外参标定和相机-IMU外参标定。

## 📋 项目概述

### 硬件配置
- **相机**: 单目相机（焦距12mm，视场角26°H×15°V）
- **IMU**: IMU传感器（型号待确定）
- **平台**: 无人机（相机和IMU固定安装）

### 标定目标
1. **IMU内参标定**：噪声密度和随机游走噪声
2. **相机内参标定**：焦距、主点、畸变参数
3. **相机-IMU外参标定**：相机与IMU间的空间变换关系

### 工具包说明
本文档中的`code_utils`、`imu_utils`、`Kalibr`等文件夹仅供参考。实际使用时需要在Ubuntu 20.04系统中按照标准ROS工作空间结构进行安装和配置。

---

## 🚀 完整标定流程

### 第一阶段：IMU内参标定

#### 1.1 环境准备（Ubuntu 20.04 + ROS Noetic）

**安装系统依赖**
```bash
sudo apt-get update
sudo apt-get install liblapack-dev libsuitesparse-dev libcxsparse3 libgflags-dev
sudo apt-get install libgoogle-glog-dev libgtest-dev libdw-dev
```

**安装Ceres-solver**
```bash
git clone https://github.com/ceres-solver/ceres-solver.git
cd ceres-solver && mkdir build && cd build
cmake .. && make && sudo make install
```

**创建ROS工作空间**
```bash
mkdir -p ~/kalibr_workspace/src && cd ~/kalibr_workspace
catkin_make
source devel/setup.bash
```

#### 1.2 安装工具包

**安装imu_utils和code_utils**
```bash
cd ~/kalibr_workspace/src
git clone https://github.com/gaowenliang/code_utils.git
git clone https://github.com/gaowenliang/imu_utils.git

# 修改CMakeLists.txt文件（重要）
# 将code_utils 和 imu_utils 的 CMakeLists.txt 文件CMAKE_CXX_FLAGS "-std=c++11"改为CMAKE_CXX_STANDARD 14
# 在code_utils的CMakeLists.txt中添加include_directories(include/code_utils)

cd ~/kalibr_workspace && catkin_make
source devel/setup.bash
```

**安装Kalibr**
```bash
# 安装依赖
sudo apt-get install python3-setuptools python3-rosinstall python3-pip libeigen3-dev libboost-all-dev doxygen libopencv-dev python3-dev python3-scipy python3-matplotlib ipython3
sudo apt-get install ros-noetic-vision-opencv ros-noetic-image-transport-plugins ros-noetic-cmake-modules libpoco-dev libtbb-dev libblas-dev liblapack-dev libv4l-dev
sudo apt-get install python3-catkin-tools

# 编译Kalibr
cd ~/kalibr_workspace/src
git clone https://github.com/ethz-asl/Kalibr.git
cd ~/kalibr_workspace
catkin build -DCMAKE_BUILD_TYPE=Release -j4
source ~/kalibr_workspace/devel/setup.bash
```

#### 1.3 数据采集

##### 连接方案选择

**方案A：USB串口连接（推荐用于标定）**

1. **硬件连接检查**
```bash
# 连接方式：IMU传感器 → 飞控板 → USB转串口模块 → USB线 → 电脑
ls /dev/ttyUSB*  # 检查串口设备
dmesg | grep tty  # 查看设备信息
```

2. **串口权限配置**
```bash
sudo chmod 666 /dev/ttyUSB0  # 临时权限
# 或永久配置（推荐）
sudo usermod -a -G dialout $USER  # 需要重新登录
```

3. **启动传感器节点**
```bash
# 确定IMU型号和通信协议
lsusb && dmesg | grep tty

# 方法一：使用rosserial（推荐）
sudo apt-get install ros-noetic-rosserial-python ros-noetic-rosserial
rosrun rosserial_python serial_node.py _port:=/dev/ttyUSB0 _baud:=115200

# 方法二：使用品牌专用驱动（根据实际IMU型号选择）
# Xsens IMU
sudo apt-get install ros-noetic-xsens-mti-driver
rosrun xsens_mti_driver xsens_mti_node

# VectorNav IMU
sudo apt-get install ros-noetic-vectornav
rosrun vectornav vectornav

# Microstrain IMU
sudo apt-get install ros-noetic-microstrain-inertial-driver
rosrun microstrain_inertial_driver microstrain_inertial_driver_node

# 方法三：测试串口通信（调试用）
sudo apt-get install minicom
minicom -D /dev/ttyUSB0 -b 115200
```

4. **启动相机节点**
```bash
sudo apt-get install ros-noetic-usb-cam
# 将默认话题 remap 为 /camera/image_raw，统一后续流程
rosrun usb_cam usb_cam_node _video_device:=/dev/video0 image_raw:=/camera/image_raw
```

**方案B：无线连接（需要机载Linux系统）**

1. **地面站配置**
```bash
export ROS_MASTER_URI=http://localhost:11311
export ROS_IP=*************
roscore
```

2. **无人机端配置（通过SSH）**
```bash
ssh pi@*************
export ROS_MASTER_URI=http://*************:11311
export ROS_IP=*************

# 启动传感器节点
rosrun imu_driver imu_node
rosrun usb_cam usb_cam_node image_raw:=/camera/image_raw
```

##### 数据采集操作

**确定话题名称**
```bash
# 查看所有话题
rostopic list | grep -E "(imu|image)"

# 验证话题类型和频率
rostopic type /imu/data    # 应该是sensor_msgs/Imu
rostopic hz /imu/data      # 检查发布频率
rostopic hz /camera/image_raw
```

**数据采集方法**

*同步采集（推荐用于联合标定）*
```bash
# 降低相机频率
rosrun topic_tools throttle messages /camera/image_raw 4.0 /camera/image_throttled

# 同时录制IMU和相机数据
rosbag record /imu/data /camera/image_throttled -O sync_imu_camera_data.bag

# 实时监控
rostopic echo /imu/data | head -5
watch -n 1 'ls -lh *.bag'
```

*单独采集*
```bash
# IMU静态数据（2小时以上）
rosbag record /imu/data -O imu_static_data.bag

# 相机标定数据
rosbag record /camera/image_throttled -O camera_calibration_data.bag
```

**数据质量验证**
```bash
# 检查时间戳同步质量
rosbag info sync_imu_camera_data.bag
#可能会输出类似下面这样的结果：
'''
path:        sync_imu_camera_data.bag
version:     2.0
duration:    45.2s
start:       Jun 15 2024 10:30:00.12 (1718433000.12)
end:         Jun 15 2024 10:30:45.32 (1718433045.32)
size:        1.2 GB
messages:    4520
compression: none [1/1 chunks]
types:       sensor_msgs/Imu      [6a62c6daae103f4ff57a132d6f95cec2]
             sensor_msgs/Image    [060021388200f6f0f447d0fcd9c64743]
topics:      /imu/data            4520 msgs @ 100.0 Hz : sensor_msgs/Imu
             /camera/image_throttled 226 msgs @ 5.0 Hz   : sensor_msgs/Image
'''

# 验证时间戳范围
rosbag play sync_imu_camera_data.bag --pause
rostopic echo /imu/data | grep stamp
rostopic echo /camera/image_throttled | grep stamp

# 检查数据频率比例（IMU: ~100Hz, 相机: ~4Hz）
rostopic hz /imu/data
rostopic hz /camera/image_throttled
```

**故障排除**
```bash
# 网络连接问题
rosnode list && rostopic list
ping *************

# 数据传输问题
rostopic hz /imu/data
rostopic echo /imu/data
rosnode kill /imu_node && rosrun imu_driver imu_node

# 存储空间检查
df -h
watch -n 1 'ls -lh *.bag'

```

#### 1.4 IMU标定

**确定实际IMU话题名称**
```bash
rostopic list | grep -i imu
# 常见话题：/imu/data, /imu/data_raw, /sensor/imu, /xsens/imu
```

**创建launch文件（my_imu.launch）**
```xml
<launch>
    <node pkg="imu_utils" type="imu_an" name="imu_an" output="screen">
        <!-- 根据实际话题名称修改 -->
        <param name="imu_topic" type="string" value="/imu/data"/>
        <param name="imu_name" type="string" value="my_imu"/>
        <param name="data_save_path" type="string" value="$(find imu_utils)/data/"/>
        <param name="max_time_min" type="int" value="120"/>
        <param name="max_cluster" type="int" value="100"/>
    </node>
</launch>
```

**执行标定**
```bash
# 启动标定（先启动订阅节点，避免丢帧）
roslaunch imu_utils my_imu.launch

# 播放IMU数据包
rosbag play -r 200 imu_static_data.bag
```
'''
会得到类似下面这样的输出结果：
%YAML:1.0
---
type: IMU
name: xsens
Gyr:
   unit: " rad/s"
   avg-axis:
      gyr_n: 4.9700389767894345e-03
      gyr_w: 6.8522312307501954e-05
   x-axis:
      gyr_n: 4.9016990946736316e-03
      gyr_w: 7.4721244172569240e-05
   y-axis:
      gyr_n: 4.9561825133283884e-03
      gyr_w: 6.7372212440839068e-05
   z-axis:
      gyr_n: 5.0522353223662842e-03
      gyr_w: 6.3473480309097542e-05
Acc:
   unit: " m/s^2"
   avg-axis:
      acc_n: 5.4303615468688114e-03
      acc_w: 1.4459759280127568e-04
   x-axis:
      acc_n: 5.7602300667994426e-03
      acc_w: 1.5064216759502606e-04
   y-axis:
      acc_n: 5.6378382747581945e-03
      acc_w: 1.4458085233539733e-04
   z-axis:
      acc_n: 4.8930162990487953e-03
      acc_w: 1.3856975847340367e-04
'''
---

### 第二阶段：相机内参标定

#### 2.1 相机数据采集

```bash
# 降低相机频率到4Hz
rosrun topic_tools throttle messages /camera/image_raw 4.0 /camera/image_throttled

# 录制相机数据
rosbag record /camera/image_throttled -O camera_images.bag
```

**数据采集要点**：
- 使用AprilGrid标定板
- 采集不同角度、距离的标定板图像
- 覆盖相机视野的各个区域

#### 2.2 相机标定

```bash
kalibr_calibrate_cameras \
    --target april_6x6_24x24mm.yaml \
    --bag camera_images.bag \
    --bag-from-to 5 30 \
    --models pinhole-radtan \
    --topics /camera/image_throttled
```
相机标定结果：
cam-report-cam.pdf 包含绘制的图片和标定的参数。
cam-results-cam.txt 以文本文件储存的标定结果。
cam-camchain.yaml 以YAML格式储存的标定结果。它可以直接用来作为相机-IMU校正的输入。
相机标定也可以使用ROS自带的camera_calibration进行标定

**模型选择说明**：
- 您的相机（12mm焦距，26°×15°视场角）属于标准针孔相机
- 使用`pinhole-radtan`模型（针孔+径向切向畸变）

---

### 第三阶段：相机-IMU联合标定

#### 3.1 联合数据采集

```bash
# 同时录制相机和IMU数据
# 注意：需要充分激励6自由度运动（3轴旋转+3轴平移）
# 确保在录制前已执行 throttle，将 /camera/image_raw 降频为 /camera/image_throttled（例：4Hz）
rosbag record /imu/data /camera/image_throttled -O camera_imu.bag

#### 3.2 准备配置文件

**IMU配置文件（imu.yaml）**
```yaml
#Accelerometers
accelerometer_noise_density: 5.43036e-03   # 从IMU标定结果获得
accelerometer_random_walk:   1.44598e-04   # 从IMU标定结果获得

#Gyroscopes
gyroscope_noise_density:     4.9700e-03    # 从IMU标定结果获得
gyroscope_random_walk:       6.8522e-05    # 从IMU标定结果获得

rostopic:                    /imu/data     # 根据实际话题修改
update_rate:                 100.0         # 根据实际IMU频率修改
```

**相机配置文件（camchain.yaml）**
```yaml
cam0:
  cam_overlaps: []
  camera_model: pinhole
  distortion_coeffs: [-0.1734857772863602, 0.026545178121976657, 0.0004291887376674085,
    -3.4873170616746686e-05]
  distortion_model: radtan
  intrinsics: [693.131838769146, 692.5498277671763, 616.3486206381017, 379.6677572220899]
  resolution: [1280, 720]
  rostopic: /camera/image_throttled
```

#### 3.3 联合标定执行

```bash
kalibr_calibrate_imu_camera \
    --target april_6x6_24x24mm.yaml \
    --cam camchain.yaml \
    --imu imu.yaml \
    --bag camera_imu.bag \
    --bag-from-to 5 50 \
    --imu-models scale-misalignment \
    --timeoffset-padding 0.1
---

## 📊 标定质量评估

### 评估指标
1. **重投影误差**: 应小于1像素
2. **IMU误差**: 陀螺仪和加速度计误差在合理范围内
3. **外参一致性**: 标定结果与物理测量基本一致

### 输出文件
- **IMU标定**: `imu_name.yaml`
- **相机标定**: `camchain.yaml`, `report.pdf`
- **联合标定**: `camchain-imucam.yaml`, `report-imucam.pdf`

---

## ⚠️ 注意事项

### 硬件特殊考虑
- **相机类型**: 标准针孔相机，使用`pinhole-radtan`模型
- **视场角小**: 需要合适的标定板尺寸和采集策略
- **话题名称**: 必须根据实际传感器话题修改配置

### 数据采集要求
- **相机频率**: 降至4Hz进行标定
- **IMU数据**: 静态采集2小时以上
- **联合标定**: 充分的6DOF运动激励
- **时间同步**: 所有ROS消息都包含时间戳

### 环境要求
- **系统**: Ubuntu 20.04 + ROS Noetic
- **照明**: 保持良好照明，避免运动模糊
- **网络**: 无线连接时确保网络稳定
---

## 🔧 快速开始检查清单

### 环境准备
- [ ] Ubuntu 20.04系统
- [ ] ROS Noetic安装
- [ ] 创建~/kalibr_workspace工作空间
- [ ] 安装ceres-solver、code_utils、imu_utils、Kalibr

### 硬件确认
- [ ] 相机类型：标准针孔相机（12mm，26°×15°）
- [ ] 畸变模型：pinhole-radtan
- [ ] IMU型号确认和话题名称
- [ ] 相机和IMU在无人机上固定安装

### 标定流程
- [ ] 第一阶段：IMU内参标定（静态2小时数据）
- [ ] 第二阶段：相机内参标定（4Hz频率，多角度数据）
- [ ] 第三阶段：联合标定（6DOF运动激励）

---

## 📚 参考资料

- [Kalibr官方文档](https://github.com/ethz-asl/kalibr/wiki)
- [imu_utils工具](https://github.com/gaowenliang/imu_utils)
- Allan方差分析技术报告
- VINS-Mono/VINS-Fusion参数配置指南


