安卓手机 相机和IMU数据获取标定 在VINS-MONO运行自己的数据集（含打包方法） （非常详细一步一步来）

 本文详细介绍了如何在Android手机上同时采集图像和IMU数据，包括使用开源库VideoIMUCapture-Android进行数据采集，利用Docker处理数据，进行相机与IMU的标定，最终实现两者的融合标定。

Android手机上图像和IMU数据采集的方法
网上有相关的教程，但都讲的很模糊，而且不全，甚至还有人要收费。自己完整做了一遍发现还是有些麻烦，固记录下来供大家参考，希望能帮到大家、

采用开源库 VideoIMUCapture-Android
可同时采集图像和IMU数据并提供了标定脚本。点击上面链接，下载软件。

第一步：获取数据
我文末的参考博客里有从linux获取软件数据的方法，这里我介绍在windows上直接获取的方法（当然linux也可以这样）
从上面的github库里面获取apk文件进行安装，打开会得到这样一个界面，点击直接开始录制（如图）


录制完成的数据会储存在
/Android/data/se.lth.math.videoimucapture/files/YYYY_MM_DD_hh_mm_ss
当然，如果你点软件界面的感叹号，他告诉你的路径前面还有前缀 /storage/emulated/0/，这个只是值你的设备的主储存。
如果你通过手机文件管理器直接查看这个位置，很有可能看不到，这是Arduino11之后的权限问题。
最直接的方法，你直接将手机数据线连接到电脑上，在手机上选择usb传输模式为文件传输，这样你就可以在电脑端直接访问手机里的这个文件夹，如图

这里你就可以看到你在不同时间段录制的数据,直接就可以拿出来用


第二步：使用docker处理采集的数据
ubuntu18安装docker，参考链接：
ubuntu18.04上安装Docker
在linux下运行如下命令，获取 VideoIMUCapture-Android 的github库

git clone https://github.com/DavidGillsjo/VideoIMUCapture-Android.git

进入对应文件夹

cd VideoIMUCapture-Android/calibration

默认方法
在calibration文件夹下运行命令，

SUDO=1 DATA=<my-data-path> ./run_dockerhub.sh

这里<my-data-path>就是你放的 YYYY_MM_DD_hh_mm_ss 这样日期文件的上一级文件，比如我这里


替代方法
受限于docker不能连接了，可以用这个百度网盘的镜像文件：
链接: https://pan.baidu.com/s/1QO1WIby3vl4SAMvyu1qYUA 提取码: jwai

然后使用
docker load -i videoimucapture-calibration.tar
加载为docker中的镜像

修改脚本禁用自动拉取
编辑 run_dockerhub.sh，将 PULL=1 改为 PULL=0，避免重复尝试拉取：
PULL=0 USE_NVIDIA=0 IMAGE=davidgillsjo/videoimucapture-calibration ./…/libs/dockers/common/run.sh “$@”

然后还是执行

SUDO=1 DATA=<my-data-path> ./run_dockerhub.sh

执行完这个就进入了docker容器里面，进入Docker容器后，默认在calibration目录。这里的calibration目录是docker中的，里面是用于处理采集的数据的Python脚本，其实这个docker容器是配置好了kalibr库，就不用我们自己去编译安装这个库了。

第三步：标定相机
先在这个位置：https://github.com/ethz-asl/kalibr/wiki/downloads下载一个 April，用A4纸打印，贴到平整的墙面上，然后用上面的软件对着它简单录制几十秒的视频。
按照上面的方法，把数据转移到linux上
首先要处理一下采集到的数据，将数据转换为kalibr可处理的数据
执行命令

python data2statistics.py /host_home/<path-to-recording>/video_meta.pb3

这里<path-to-recording>就是<my-data-path>/YYYY_MM_DD_hh_mm_ss,比如我这

运行后会弹出这样的图片，关掉就好了

kalibr标定
运行命令

python data2kalibr.py /host_home/<path-to-recording> --tag-size <measured-april-tag-size-in-meters> --subsample 30

这里–tag-size表示我们自己打印的Apritag后，用尺子测量的一个二维码大小单位是m，–subsample为从.mp4文件中采样图片间隔，30表示每隔30帧采样一张。
比如我这


这个脚本执行完后，如上图，会在数据存储的目录生成一个kalibr目录，用于存储处理好的数据。
进入到kalibr目录

cd /host_home/<path-to-recording>/kalibr

执行标定命令
这里发布的topic是/cam0/image_raw

kalibr_calibrate_cameras --bag kalibr.bag --target target.yaml --models pinhole-equi --topics /cam0/image_raw

第四步：标定IMU
由于IMU的加速度计和陀螺仪有噪声和游走噪声，需要单独的标定，目前都是采用imu_utils库来标定。
首先我们要在Ubuntu中安装ros，并编译imu_utils。
ros的安装可以参考我的另一篇博客：
https://blog.csdn.net/qin_liang/article/details/127352107

imu_utils编译按以下方法
安装imu_utils

先安装依赖

sudo apt-get install libdw-dev

先编译code_utils

mkdir -p imu-calibration/src
cd imu-calibration/src
git clone https://github.com/gaowenliang/code_utils.git
cd ..

在code_utils下面找到sumpixel_test.cpp，修改#include "backward.hpp"为

#include "code_utils/backward.hpp"

再编译

catkin_make

cd src
git clone https://github.com/gaowenliang/imu_utils.git
cd ..
catkin_make

获取imu数据
这里我们用另一个App单独采集IMU数据，点击链接下载
Mobile AR Sensor (MARS) Logger

开始录制后，将手机平放在桌面上静止11分钟。（官方推荐2小时，我这里用11分钟先试一下）
采集的数据存储在手机的、Android/data/edu.osu.pcv.marslogger/files/data/路径，找的方法同上

存储的格式为

修改一下采集到的gro_accel.csv的第一行为如下图所示，并改名成imu0.csv


将imu.csv转化为ros.bag
在doker环境下的kalibr文件夹下运行命令（根据自己的imu数据存放位置改，bag包名字可以改）

kalibr_bagcreater --folder /host_home/dataset/imu/ --output-bag imu.bag

比如我的

创建launch文件
打开之前miu_utils下的lauch文件夹，创建android.launch

里面内容如下：

<launch>
	<node pkg="imu_utils" type="imu_an" name="imu_an" output="screen">
		<param name="imu_topic" type="string" value= "/imu0"/>
		<param name="imu_name" type="string" value= "onePlus7"/>
		<param name="data_save_path" type="string" value= "$(find imu_utils)/data/"/>
		<param name="max_time_min" type="int" value= "11"/>
		<param name="max_cluster" type="int" value= "100"/>
	</node>
</launch>

imu_name可以自定义，比如我的是onePlus7

max_time_min表示采集imu的数据的时间，建议2个小时，单位是分钟，我这里只采集的11分钟

启动标定
如果下面的命令执行出错，可以先在imu_utils所在的空间下，执行catkin_make。如果是按照我之前的方法安装的imu_utils就是如下


在launch对应的文件夹下打开终端
输入命令（这里根据你imu_utils所在的位置更改）

source ~/imu-calibration/devel/setup.bash

再执行命令

roslaunch imu_utils android.launch

此时程序会进入等待话题的状态

这时打开之前转化的.bag包的位置，执行命令(bag名自己改)

rosbag play imu.bag

你也可以使用以下命令，以200倍速度播放

rosbag play -r 200 imu.bag

要是这里失败了就重新生成imu.bag
终端会开始读取数据

运行lauch的终端出现这个done的时候，就完成了标定（我当时只运行到log file的位置，等了一段时间还没结束，我看文件已经生成了就直接ctrl+C结束了终端，最后也显示了done）


完成后，你就可以在imu_utils的data文件夹下看到生成的标定文件


第五步：相机和IMU融合标定
相机和IMU融合标定是ApilTag不动
按照https://github.com/ethz-asl/kalibr/wiki/camera-imu-calibration 给的方式采集数据。
首先通过python data2kalibr.py 处理记录的数据，

python data2kalibr.py /host_home/<path-to-recording> --tag-size 0.025 --subsample 3 --kalibr-calibration <calibration-result-dir>/camchain-kalibr.yaml

这里 --kalibr-calibration指向我们在相机标定步骤生成的标定文件
比如我这

把我们之前标定的IMU数据中对应项（avg）填到到imu.yaml中（前面标定的IMU数据用的缩写）

然后改了一下rostopic,我的修改如下



切换到kalibra文件夹后再执行

cd <path-to-recording>/kalibr

kalibr_calibrate_imu_camera --target target.yaml --imu imu.yaml --cams camchain.yaml --bag kalibr.bag



这里可以看到就已经标定完成了

转化ros.bag包
进入对应文件夹

cd VideoIMUCapture-Android/calibration

在calibration文件夹下运行命令，

SUDO=1 DATA=<my-data-path> ./run_dockerhub.sh
AI写代码
bash
1
再运行（这里的camchain-imucam-kalibr.yaml，要具体写之前我们生成这个文件的位置，

python data2rosbag.py <path-to-recording> --calibration camchain-imucam-kalibr.yaml --raw-image
AI写代码
bash
1
比如我的

python data2rosbag.py /host_home/dataset/test5 --calibration /host_home/dataset/cam_imu/kalibr/camchain-imucam-kalibr.yaml --raw-image
AI写代码
bash
1
这样在这个文件夹下就生成了data.bag的包


在VINS上运行
打开VINS的config文件夹，新建一个android的文件夹
将euroc文件夹下的euroc_config.yaml复制到android的文件夹下，改名为android_config.yaml

修改里面的image_width,image_height,这里注意那个手机APP比如显示的分辨率是1280x960，你这里width要写960,height写1280,因为你手机是竖屏的。（千万不要把model_type改为小写，可能之前那个标定文件里是小写，但这里要保持大写，要不然后面跑的时候你看到的都是蓝色的点）
打开之前的标定文件，把imu和cam的参数填到andoid_config.yaml中

在VINS的文件夹中复制euroc.launch一份，改名为android.launch，修改config_path为我们刚才创建的android_config.yaml的文件夹


开始运行
在catkin_ws文件夹下分别打开三个终端，分别输入

source devel/setup.bash
roslaunch vins_estimator android.launch

source devel/setup.bash
roslaunch vins_estimator vins_rviz.launch

这里就是你自己数据集生成的bag包的位置

source devel/setup.bash
rosbag play data5.bag

